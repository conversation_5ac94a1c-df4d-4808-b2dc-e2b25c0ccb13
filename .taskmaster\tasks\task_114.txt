# Task ID: 114
# Title: Implement Task File Generation from tasks.json
# Status: pending
# Dependencies: 109, 111
# Priority: medium
# Description: Implement a system to generate individual `.txt` files for each task in the `tasks/` directory from the main `tasks.json` file.
# Details:
Create a `generate-files` command. This command will iterate through all tasks in `tasks.json`. For each task, it will create a file named `task_<id>.txt` (e.g., `task_001.txt`) using the format specified in the PRD. Use padding for the ID in the filename for sorting.

# Test Strategy:
Populate `tasks.json` with a few tasks. Run `generate-files`. Verify that the `tasks/` directory contains the correct number of files with the correct names and that the content of each file matches the PRD format.
