#!/usr/bin/env node

import { FastMCP } from 'fastmcp';
import path from 'path';
import fs from 'fs';

// Log the current directory
console.error(`Current working directory: ${process.cwd()}`);

try {
	console.error('Attempting to test FastMCP...');

	// Check if .cursor/mcp.json exists
	const mcpPath = path.join(process.cwd(), '.cursor', 'mcp.json');
	console.error(`Checking if mcp.json exists at: ${mcpPath}`);

	if (fs.existsSync(mcpPath)) {
		console.error('mcp.json file found');
		const mcpConfig = JSON.parse(fs.readFileSync(mcpPath, 'utf8'));
		console.error(`File content: ${JSON.stringify(mcpConfig, null, 2)}`);

		// Check if environment variables are set
		if (mcpConfig.mcpServers && mcpConfig.mcpServers['taskmaster-ai'] && mcpConfig.mcpServers['taskmaster-ai'].env) {
			console.error('Environment variables found in mcp.json:');
			const envVars = mcpConfig.mcpServers['taskmaster-ai'].env;
			for (const [key, value] of Object.entries(envVars)) {
				if (key.includes('KEY')) {
					console.error(`${key}: [value hidden]`);
				} else {
					console.error(`${key}: ${value}`);
				}
			}
		}
	} else {
		console.error('mcp.json file not found');
	}

	// Try to create FastMCP instance
	const server = new FastMCP({
		name: 'Test MCP Server',
		version: '1.0.0'
	});
	console.error('FastMCP server instance created successfully');

	// Add a simple test tool
	server.addTool({
		name: 'test_echo',
		description: 'A simple echo tool for testing',
		inputSchema: {
			type: 'object',
			properties: {
				message: {
					type: 'string',
					description: 'Message to echo back'
				}
			},
			required: ['message']
		}
	}, async ({ message }) => {
		return `ECHO: ${message}`;
	});

	console.error('Test tool added successfully');
	console.error('FastMCP test completed successfully!');

} catch (error) {
	console.error(`Error testing FastMCP: ${error.message}`);
	console.error(`Stack trace: ${error.stack}`);
}

// Log process.env to see if values from mcp.json were loaded automatically
console.error('\nChecking if process.env already has values from mcp.json:');
const envVars = [
	'ANTHROPIC_API_KEY',
	'PERPLEXITY_API_KEY',
	'MODEL',
	'PERPLEXITY_MODEL',
	'MAX_TOKENS',
	'TEMPERATURE',
	'DEFAULT_SUBTASKS',
	'DEFAULT_PRIORITY'
];

for (const varName of envVars) {
	if (process.env[varName]) {
		if (varName.includes('KEY')) {
			console.error(`${varName}: [value hidden]`);
		} else {
			console.error(`${varName}: ${process.env[varName]}`);
		}
	} else {
		console.error(`${varName}: not set`);
	}
}
