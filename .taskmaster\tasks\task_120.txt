# Task ID: 120
# Title: Implement Task Expansion with <PERSON>
# Status: pending
# Dependencies: 112, 117
# Priority: medium
# Description: Create a command to expand a high-level task into detailed subtasks using the Claude API.
# Details:
Add an `expand` command taking a `--id=<id>`. It will fetch the specified task, use the 'Task Expansion Prompt Structure' to query <PERSON>, parse the response, and add the generated subtasks to the `subtasks` array of the parent task in `tasks.json`.

# Test Strategy:
Create a parent task. Run `expand --id=<id>`. Mock the Claude API response with a list of subtasks. Verify that the `subtasks` array in `tasks.json` for the specified task is correctly populated.
