# Task ID: 104
# Title: Implement 'scope-up' and 'scope-down' CLI Commands for Dynamic Task Complexity Adjustment
# Status: pending
# Dependencies: 3, 11, 19, 94
# Priority: high
# Description: Add new CLI commands 'scope-up' and 'scope-down' to enable users to dynamically increase or decrease the complexity of tasks or subtasks, with support for multiple IDs, strength levels, and milestone-aware adjustments.
# Details:
1. Extend the CLI (commands.js) to introduce 'scope-up' and 'scope-down' commands, following established subcommand patterns for consistency and discoverability. 
2. Accept comma-separated task/subtask IDs and an optional '--strength' flag (light|regular|heavy, defaulting to regular). Validate all inputs, ensuring referenced tasks/subtasks exist and strength is valid.
3. Fetch current task details and associated milestone context to inform the adjustment logic.
4. Implement core logic functions that:
   - Construct context-aware AI prompts for scaling complexity up or down, leveraging the current state, project phase, and strength parameter.
   - Call the unified AI service (ai-services-unified.js) to generate new task/subtask content at the desired complexity level.
   - Replace existing task details and subtasks with the AI-generated output, preserving historical versions for rollback/comparison.
   - Ensure task dependency integrity and update tasks.json and related files accordingly.
5. Integrate robust error handling for invalid IDs, parameters, or AI failures, and provide clear CLI output showing before/after differences.
6. Add corresponding MCP tool equivalents for integrated environments and update documentation/help text to reflect new commands and usage patterns.
7. Ensure compatibility with batch operations, milestone-based guidelines, and existing task management workflows.

# Test Strategy:
- Write unit and integration tests for both 'scope-up' and 'scope-down' commands, covering single and multiple ID scenarios, all strength levels, and edge cases (e.g., non-existent IDs, invalid strength values).
- Simulate CLI usage to verify correct parsing, validation, and error handling.
- Test AI prompt construction and output integration, ensuring that task complexity is adjusted as expected for each strength level and milestone context.
- Verify that historical data is preserved and that before/after summaries are accurate and clear.
- Confirm that task dependencies remain intact and that batch operations work as intended.
- Validate MCP tool integration and documentation updates.

# Subtasks:
## 2. Set up Authentication System and User Management Backend [pending]
### Dependencies: None
### Description: Implement the complete authentication infrastructure including user registration, login, password reset, and session management with database schema and API endpoints.
### Details:
Create user database schema with fields for id, email, password_hash, name, bio, avatar_url, created_at, updated_at. Implement JWT-based authentication with refresh tokens. Set up API endpoints for /auth/register, /auth/login, /auth/logout, /auth/refresh, /auth/forgot-password, /auth/reset-password, /auth/verify-email. Include password hashing with bcrypt, email verification system, and role-based access control with user roles (admin, user). Implement middleware for route protection and session validation.

