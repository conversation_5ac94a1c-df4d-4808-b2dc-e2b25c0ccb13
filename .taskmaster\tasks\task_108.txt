# Task ID: 108
# Title: Define Core Data Models in TypeScript
# Status: pending
# Dependencies: 107
# Priority: high
# Description: Define the TypeScript interfaces for the Task and TasksCollection models based on the PRD's JSON schemas.
# Details:
Create a `src/models.ts` file. Define `Subtask`, `Task`, `TasksMeta`, and `TasksCollection` interfaces. Use types for status ('pending' | 'done' | 'deferred') and priority ('high' | 'medium' | 'low'). These interfaces will ensure type safety throughout the application.

# Test Strategy:
The TypeScript compiler should pass without errors when using these interfaces. No runtime tests needed for this task.
