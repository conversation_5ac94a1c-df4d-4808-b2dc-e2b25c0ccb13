# Task ID: 122
# Title: Integrate Perplexity API with <PERSON> Fallback
# Status: pending
# Dependencies: 116, 117
# Priority: medium
# Description: Integrate the Perplexity API for research-oriented queries, with a fallback to <PERSON> if Perplexity is unavailable.
# Details:
Install the `openai` package. Create `src/perplexityService.ts`. Use the OpenAI client configured for the Perplexity API endpoint. Implement a function `invokePerplexity(prompt: string)`. In the main application logic, wrap calls to this service in a try-catch block that calls `claudeService` on failure.

# Test Strategy:
Unit test the Perplexity service by mocking the `openai` client. Create an integration test that simulates a Perplexity API failure and confirms the system correctly falls back to the Claude service.
