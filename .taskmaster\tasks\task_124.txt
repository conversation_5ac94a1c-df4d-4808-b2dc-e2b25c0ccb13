# Task ID: 124
# Title: Implement Batch Operation for Task Updates
# Status: pending
# Dependencies: 112
# Priority: low
# Description: Allow users to update the status of multiple tasks in a single command.
# Details:
Enhance the `update` command to accept a comma-separated list of IDs via `--ids=<id1,id2,...>`. The command will parse the list, iterate through the IDs, and apply the specified `--status` or `--priority` to each task.

# Test Strategy:
Run `update --ids=1,2,3 --status=done`. Check `tasks.json` to confirm that all three tasks have been updated. Test with invalid IDs in the list.
