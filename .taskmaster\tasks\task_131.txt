# Task ID: 131
# Title: Implement API Error Handling and Retries
# Status: pending
# Dependencies: 117, 122
# Priority: medium
# Description: Implement robust error handling and retry mechanisms for all external API calls.
# Details:
In `claudeService.ts` and `perplexityService.ts`, wrap API calls with a retry logic, such as exponential backoff. Use a library like `async-retry`. The system should handle common HTTP errors (429, 500, 503) gracefully and retry a configurable number of times before failing.

# Test Strategy:
Unit test the retry logic by mocking API calls to throw specific errors. Verify that the service retries the correct number of times for a 429 error and fails immediately for a 401 error.
