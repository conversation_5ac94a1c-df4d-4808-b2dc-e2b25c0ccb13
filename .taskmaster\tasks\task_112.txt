# Task ID: 112
# Title: Implement 'update' Command for Task Status and Priority
# Status: pending
# Dependencies: 109, 110
# Priority: medium
# Description: Implement a CLI command to update the status and priority of an existing task.
# Details:
Add an `update` command. It should accept `--id=<id>` (required), and optional `--status=<status>` and `--priority=<priority>` arguments. The command will read `tasks.json`, find the specified task, update its properties, and write the file back.

# Test Strategy:
Run `update` with a valid ID and new status. Use the `list` command or inspect `tasks.json` to verify the change. Test edge cases like invalid ID or invalid status/priority values.
