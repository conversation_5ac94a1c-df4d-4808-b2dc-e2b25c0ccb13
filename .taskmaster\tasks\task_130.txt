# Task ID: 130
# Title: Create Comprehensive User Documentation (README)
# Status: pending
# Dependencies: 110
# Priority: medium
# Description: Create a comprehensive README.md file for the project, detailing setup, configuration, and usage of all CLI commands.
# Details:
Update the main `README.md`. Include sections for: Installation (Node.js version, `npm install`), Configuration (creating and filling `.env`), and a Command Reference with examples for every command and its options.

# Test Strategy:
Give the `README.md` to a new developer. They should be able to set up the project and use its basic features successfully without any other assistance.
