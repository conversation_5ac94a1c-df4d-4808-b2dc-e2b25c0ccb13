# Task ID: 136
# Title: Create CLI Command to Verify API Key Connectivity
# Status: pending
# Dependencies: 131, 130
# Priority: medium
# Description: Develop a new CLI command that allows a user to manually test the connectivity and validity of their configured API key for a specific AI service, providing clear success or failure feedback.
# Details:
Implement a new command, `task-master api:test <service>`, where `<service>` can be 'claude' or 'perplexity'. This command should be added to the main CLI entry point. It will read the corresponding API key (e.g., `CLAUDE_API_KEY`) from the environment variables. The command will then use the existing service module (e.g., `claudeService.ts`) to make a simple, low-cost API call, such as asking for a one-word response. It must leverage the error handling and retry logic from Task #131 to provide informative output to the user. On success, print a message like '✅ Successfully connected to the Claude API.'. On failure, print a detailed error message, e.g., '❌ Failed to connect to the Perplexity API: AuthenticationError - Invalid API Key.'

# Test Strategy:
1. Run `task-master api:test claude` with a valid `CLAUDE_API_KEY` in the `.env` file and verify a success message is printed to the console. 2. Run the same command with an invalid API key and verify a clear authentication error is displayed. 3. Remove the API key from the `.env` file and verify the command outputs a message instructing the user to set it. 4. Run `task-master api:test invalid-service` and confirm that the CLI returns an error listing the valid services. 5. After this task is complete, ensure the `README.md` (from Task #130) is updated to include documentation for this new command.

# Subtasks:
## 1. Define the 'api:test <service>' CLI Command Structure [pending]
### Dependencies: None
### Description: Set up the new `api:test <service>` command in the main CLI entry point. This includes defining the command, its argument, and implementing the initial logic to parse the service name and retrieve the corresponding API key from environment variables.
### Details:
In the main CLI file (e.g., using `commander.js`), add a new command: `.command('api:test <service>', 'Test API key connectivity for a service')`. In the action handler for this command, validate that the `<service>` argument is either 'claude' or 'perplexity'. Based on the service, construct the expected environment variable name (e.g., 'CLAUDE_API_KEY') and read its value using `process.env`. If the service is invalid or the key is missing, log an appropriate error and exit. For now, the handler can just log the service name and the retrieved key for verification.

## 2. Implement the Generic API Connectivity Test Logic [pending]
### Dependencies: None
### Description: Create a new function that performs a simple, low-cost API call to a specified service to verify connectivity. This function will dynamically use the correct service module based on the input.
### Details:
Create a new utility function, e.g., `verifyServiceConnectivity(service: string)`. This function will contain a switch statement for the `service` parameter ('claude', 'perplexity'). For each case, it will import the corresponding service module (e.g., `claudeService.ts`) and execute a simple, low-cost API call, such as asking for a one-word response like 'ping'. The function should be asynchronous and return a success indicator on a 2xx response, or throw an error on failure. This function should leverage the existing error handling and retry logic from Task #131.

## 3. Integrate API Test Logic and Implement User Feedback [pending]
### Dependencies: None
### Description: Integrate the `verifyServiceConnectivity` function into the `api:test` command's action handler. Implement the final user-facing success and failure messages based on the outcome of the API call.
### Details:
Modify the action handler for the `api:test` command created in subtask 137. Inside a `try/catch` block, call the `verifyServiceConnectivity` function (from subtask 138), passing the service name. If the promise resolves successfully, print a formatted success message to the console (e.g., '✅ Successfully connected to the Claude API.'). In the `catch` block, inspect the error object (which should be a custom error type from Task #131) and print a detailed, user-friendly failure message (e.g., '❌ Failed to connect to the Perplexity API: AuthenticationError - Invalid API Key.').

