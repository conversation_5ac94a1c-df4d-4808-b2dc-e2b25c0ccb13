# Task ID: 128
# Title: Implement JSON Output for Agent Command Integration
# Status: pending
# Dependencies: 110
# Priority: medium
# Description: Ensure all CLI commands can output in a machine-readable JSON format for agent integration.
# Details:
Refactor all commands (`list`, `update`, etc.) to check for the global `--json` flag. If the flag is present, the command should suppress human-readable output (tables, colors) and instead print the resulting data (e.g., the list of tasks, the updated task object) to stdout as a stringified JSON object.

# Test Strategy:
For each command, run it with and without the `--json` flag. Verify the standard output is a readable table/message, and the JSON output is a valid, parseable JSON string representing the correct data.
