# Task ID: 109
# Title: Implement File System Utilities for tasks.json
# Status: pending
# Dependencies: 108
# Priority: high
# Description: Create a utility module for reading from and writing to the `tasks.json` file, ensuring atomic operations.
# Details:
Create a `src/fileSystem.ts` module. Implement `readTasks(): Promise<TasksCollection>` and `writeTasks(data: TasksCollection): Promise<void>`. Use `fs/promises` for asynchronous operations. The `writeTasks` function should use `JSON.stringify(data, null, 2)` for readability.

# Test Strategy:
Unit test both functions. Test reading a mock `tasks.json`, and test writing data and verifying the file content matches exactly.
