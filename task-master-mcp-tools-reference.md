# 🔧 **Task Master MCP Tools Reference**

Here's a comprehensive guide to all the MCP tools and their parameters:

## 🏗️ **Project Setup Tools**

### `initialize_project_taskmaster-ai`
**Purpose:** Initialize a new Task Master project

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Absolute path to project root
  "rules": ["cursor", "claude", "gemini"],                   // Optional: Rule profiles to include
  "initGit": true,                                          // Optional: Initialize Git repo (default: true)
  "storeTasksInGit": true,                                  // Optional: Store tasks in Git (default: true)
  "addAliases": true,                                       // Optional: Add shell aliases (default: true)
  "skipInstall": false,                                     // Optional: Skip dependency install (default: false)
  "yes": true                                              // Optional: Skip prompts (default: true for MCP)
}
```

### `models_taskmaster-ai`
**Purpose:** Get/set AI model configuration

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "listAvailableModels": true,                              // Optional: List all available models
  "setMain": "gemini-2.5-pro-preview-05-06",               // Optional: Set primary model
  "setFallback": "claude-3-5-sonnet-20241022",             // Optional: Set fallback model
  "setResearch": "perplexity-llama-3.1-sonar-large-128k-online", // Optional: Set research model
  "azure": false,                                           // Optional: Custom Azure model flag
  "bedrock": false,                                         // Optional: Custom Bedrock model flag
  "ollama": false,                                          // Optional: Custom Ollama model flag
  "openrouter": false,                                      // Optional: Custom OpenRouter model flag
  "vertex": false                                           // Optional: Custom Vertex AI model flag
}
```

---

## 📋 **Task Management Tools**

### `get_tasks_taskmaster-ai`
**Purpose:** Retrieve tasks with filtering options

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "status": "pending",                                       // Optional: Filter by status (pending, done, in-progress, etc.)
  "withSubtasks": true,                                     // Optional: Include subtasks in response
  "file": "tasks/tasks.json",                               // Optional: Custom tasks file path
  "complexityReport": ".taskmaster/reports/complexity.json" // Optional: Include complexity data
}
```

### `get_task_taskmaster-ai`
**Purpose:** Get detailed info about specific task(s)

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "id": "107,108,109",                                       // REQUIRED: Task ID(s) - comma-separated for multiple
  "status": "pending",                                       // Optional: Filter subtasks by status
  "file": "tasks/tasks.json",                               // Optional: Custom tasks file path
  "complexityReport": ".taskmaster/reports/complexity.json" // Optional: Include complexity data
}
```

### `set_task_status_taskmaster-ai`
**Purpose:** Update task status

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "id": "107",                                               // REQUIRED: Task/subtask ID (e.g., "15" or "15.2")
  "status": "in-progress",                                   // REQUIRED: New status (pending, done, in-progress, review, deferred, cancelled)
  "file": "tasks/tasks.json",                               // Optional: Custom tasks file path
  "tag": "master",                                          // Optional: Tag context
  "complexityReport": ".taskmaster/reports/complexity.json" // Optional: Complexity report path
}
```

### `next_task_taskmaster-ai`
**Purpose:** Find the next task to work on

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "file": "tasks/tasks.json",                               // Optional: Custom tasks file path
  "complexityReport": ".taskmaster/reports/complexity.json" // Optional: Include complexity data
}
```

---

## ➕ **Task Creation & Modification Tools**

### `add_task_taskmaster-ai`
**Purpose:** Add a new task using AI

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "prompt": "Create user authentication system with JWT",    // Optional: AI prompt for task creation
  "title": "User Authentication",                           // Optional: Manual task title
  "description": "Implement JWT-based auth",                // Optional: Manual description
  "details": "Use bcrypt for passwords, JWT for tokens",    // Optional: Manual implementation details
  "testStrategy": "Unit tests for auth functions",          // Optional: Manual test strategy
  "priority": "high",                                       // Optional: Task priority (high, medium, low)
  "dependencies": "116,117",                                // Optional: Comma-separated dependency IDs
  "research": true,                                         // Optional: Use research capabilities
  "file": "tasks/tasks.json"                               // Optional: Custom tasks file path
}
```

### `add_subtask_taskmaster-ai`
**Purpose:** Add subtask to existing task

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "id": "117",                                               // REQUIRED: Parent task ID
  "title": "Implement error handling",                      // Optional: Subtask title
  "description": "Add try-catch blocks",                    // Optional: Subtask description
  "details": "Handle API errors gracefully",                // Optional: Implementation details
  "status": "pending",                                      // Optional: Initial status
  "dependencies": "117.1,117.2",                           // Optional: Subtask dependencies
  "taskId": "118",                                          // Optional: Convert existing task to subtask
  "file": "tasks/tasks.json",                               // Optional: Custom tasks file path
  "tag": "master",                                          // Optional: Tag context
  "skipGenerate": false                                     // Optional: Skip task file generation
}
```

### `expand_task_taskmaster-ai`
**Purpose:** Expand task into subtasks

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "id": "117",                                               // REQUIRED: Task ID to expand
  "num": "6",                                               // Optional: Number of subtasks to generate
  "prompt": "Focus on error handling and security",         // Optional: Additional context
  "research": true,                                         // Optional: Use research for generation
  "force": false,                                           // Optional: Force expansion if subtasks exist
  "file": "tasks/tasks.json",                               // Optional: Custom tasks file path
  "tag": "master"                                           // Optional: Tag context
}
```

### `expand_all_taskmaster-ai`
**Purpose:** Expand all pending tasks

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "num": "5",                                               // Optional: Target subtasks per task
  "prompt": "Focus on implementation details",              // Optional: Context for all expansions
  "research": true,                                         // Optional: Use research-backed expansion
  "force": false,                                           // Optional: Force regeneration of existing subtasks
  "file": "tasks/tasks.json"                               // Optional: Custom tasks file path
}
```

---

## 🔄 **Task Update Tools**

### `update_task_taskmaster-ai`
**Purpose:** Update a single task with new information

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "id": "117",                                               // REQUIRED: Task ID to update
  "prompt": "Add TypeScript support and better error handling", // REQUIRED: Update information
  "append": false,                                          // Optional: Append timestamped info instead of full update
  "research": true,                                         // Optional: Use research for updates
  "file": "tasks/tasks.json"                               // Optional: Custom tasks file path
}
```

### `update_taskmaster-ai`
**Purpose:** Update multiple tasks from a starting ID

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "from": "120",                                            // REQUIRED: Starting task ID (inclusive)
  "prompt": "Add React integration to all frontend tasks",  // REQUIRED: Update context
  "research": true,                                         // Optional: Use research for updates
  "file": "tasks/tasks.json",                               // Optional: Custom tasks file path
  "tag": "master"                                           // Optional: Tag context
}
```

### `update_subtask_taskmaster-ai`
**Purpose:** Update a specific subtask

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "id": "117.3",                                            // REQUIRED: Subtask ID (format: "parentId.subtaskId")
  "prompt": "Add validation for API responses",             // REQUIRED: Update information
  "research": true,                                         // Optional: Use research for updates
  "file": "tasks/tasks.json"                               // Optional: Custom tasks file path
}
```

---

## 🗑️ **Task Removal Tools**

### `remove_task_taskmaster-ai`
**Purpose:** Remove task or subtask permanently

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "id": "118,119",                                          // REQUIRED: Task/subtask ID(s) - comma-separated
  "confirm": true,                                          // Optional: Skip confirmation prompt
  "file": "tasks/tasks.json",                               // Optional: Custom tasks file path
  "tag": "master"                                           // Optional: Tag context
}
```

### `remove_subtask_taskmaster-ai`
**Purpose:** Remove subtask from parent task

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "id": "117.4",                                            // REQUIRED: Subtask ID (format: "parentId.subtaskId")
  "convert": false,                                         // Optional: Convert to standalone task instead of deleting
  "file": "tasks/tasks.json",                               // Optional: Custom tasks file path
  "skipGenerate": false                                     // Optional: Skip task file regeneration
}
```

### `clear_subtasks_taskmaster-ai`
**Purpose:** Clear subtasks from specified tasks

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "id": "117,118,119",                                      // Optional: Task IDs to clear subtasks from
  "all": false,                                             // Optional: Clear subtasks from all tasks
  "file": "tasks/tasks.json",                               // Optional: Custom tasks file path
  "tag": "master"                                           // Optional: Tag context
}
```

---

## 🔗 **Dependency Management Tools**

### `add_dependency_taskmaster-ai`
**Purpose:** Add dependency relationship between tasks

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "id": "118",                                              // REQUIRED: Task that will depend on another
  "dependsOn": "117",                                       // REQUIRED: Task that becomes a dependency
  "file": "tasks/tasks.json"                               // Optional: Custom tasks file path
}
```

### `remove_dependency_taskmaster-ai`
**Purpose:** Remove dependency from task

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "id": "118",                                              // REQUIRED: Task to remove dependency from
  "dependsOn": "117",                                       // REQUIRED: Dependency to remove
  "file": "tasks/tasks.json"                               // Optional: Custom tasks file path
}
```

### `validate_dependencies_taskmaster-ai`
**Purpose:** Check for dependency issues

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "file": "tasks/tasks.json"                               // Optional: Custom tasks file path
}
```

### `fix_dependencies_taskmaster-ai`
**Purpose:** Automatically fix invalid dependencies

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "file": "tasks/tasks.json",                               // Optional: Custom tasks file path
  "tag": "master"                                           // Optional: Tag context
}
```

---

## 📊 **Analysis & Reporting Tools**

### `analyze_project_complexity_taskmaster-ai`
**Purpose:** Analyze task complexity and generate recommendations

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "ids": "117,118,119",                                     // Optional: Specific task IDs to analyze
  "from": 115,                                              // Optional: Starting task ID in range
  "to": 125,                                                // Optional: Ending task ID in range
  "threshold": 7,                                           // Optional: Complexity threshold (1-10, default: 5)
  "research": true,                                         // Optional: Use research-backed analysis
  "file": "tasks/tasks.json",                               // Optional: Custom tasks file path
  "output": ".taskmaster/reports/complexity-report.json"    // Optional: Output file path
}
```

### `complexity_report_taskmaster-ai`
**Purpose:** Display complexity analysis report

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "file": ".taskmaster/reports/task-complexity-report.json" // Optional: Report file path
}
```

---

## 📄 **File Generation Tools**

### `generate_taskmaster-ai`
**Purpose:** Generate individual task files

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "file": "tasks/tasks.json",                               // Optional: Tasks file path
  "output": "tasks/"                                        // Optional: Output directory
}
```

### `parse_prd_taskmaster-ai`
**Purpose:** Parse PRD document into tasks

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "input": ".taskmaster/docs/prd.txt",                      // Optional: PRD file path (default)
  "output": ".taskmaster/tasks/tasks.json",                 // Optional: Output tasks file
  "numTasks": "25",                                         // Optional: Number of tasks to generate
  "research": true,                                         // Optional: Use research for generation
  "append": false,                                          // Optional: Append to existing tasks
  "force": false                                            // Optional: Overwrite existing file
}
```

---

## 🏷️ **Tag Management Tools**

### `list_tags_taskmaster-ai`
**Purpose:** List all available tags

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "file": "tasks/tasks.json",                               // Optional: Tasks file path
  "showMetadata": true                                      // Optional: Include metadata in output
}
```

### `add_tag_taskmaster-ai`
**Purpose:** Create new tag

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "name": "feature-auth",                                   // REQUIRED: Tag name
  "description": "Authentication feature tasks",            // Optional: Tag description
  "copyFromCurrent": false,                                 // Optional: Copy tasks from current tag
  "copyFromTag": "master",                                  // Optional: Copy from specific tag
  "fromBranch": false,                                      // Optional: Create from git branch name
  "file": "tasks/tasks.json"                               // Optional: Tasks file path
}
```

### `use_tag_taskmaster-ai`
**Purpose:** Switch to different tag context

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "name": "feature-auth",                                   // REQUIRED: Tag name to switch to
  "file": "tasks/tasks.json"                               // Optional: Tasks file path
}
```

### `delete_tag_taskmaster-ai`
**Purpose:** Delete existing tag

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "name": "old-feature",                                    // REQUIRED: Tag name to delete
  "yes": true,                                              // Optional: Skip confirmation (default: true for MCP)
  "file": "tasks/tasks.json"                               // Optional: Tasks file path
}
```

### `rename_tag_taskmaster-ai`
**Purpose:** Rename existing tag

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "oldName": "old-name",                                    // REQUIRED: Current tag name
  "newName": "new-name",                                    // REQUIRED: New tag name
  "file": "tasks/tasks.json"                               // Optional: Tasks file path
}
```

### `copy_tag_taskmaster-ai`
**Purpose:** Copy existing tag to create new tag

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "sourceName": "master",                                   // REQUIRED: Source tag to copy from
  "targetName": "feature-copy",                             // REQUIRED: New tag name
  "description": "Copy of master for feature work",         // Optional: Description for new tag
  "file": "tasks/tasks.json"                               // Optional: Tasks file path
}
```

---

## 🔍 **Research Tools**

### `research_taskmaster-ai`
**Purpose:** Perform AI-powered research with project context

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "query": "Best practices for JWT authentication in Node.js", // REQUIRED: Research query
  "taskIds": "117,118",                                     // Optional: Task IDs for context
  "filePaths": "src/auth.js,docs/api.md",                  // Optional: File paths for context
  "customContext": "We're using Express.js and TypeScript", // Optional: Additional context
  "detailLevel": "high",                                    // Optional: Detail level (low, medium, high)
  "includeProjectTree": true,                               // Optional: Include project structure
  "saveTo": "117.2",                                        // Optional: Save to specific task/subtask
  "saveToFile": true                                        // Optional: Save to research directory
}
```

---

## 🌐 **Localization Tools**

### `response-language_taskmaster-ai`
**Purpose:** Set response language for the project

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "language": "English"                                     // REQUIRED: Language (e.g., "中文", "English", "español")
}
```

---

## 🔧 **Rule Management Tools**

### `rules_taskmaster-ai`
**Purpose:** Add or remove rule profiles

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "action": "add",                                          // REQUIRED: "add" or "remove"
  "profiles": ["cursor", "claude", "gemini"],               // REQUIRED: Rule profiles array
  "force": false                                            // Optional: Force removal (dangerous)
}
```

---

## 📦 **Task Movement Tools**

### `move_task_taskmaster-ai`
**Purpose:** Move task or subtask to new position

```json
{
  "projectRoot": "C:\\Users\\<USER>\\proto\\claude-task-master",  // REQUIRED: Project directory
  "from": "117,118,119",                                    // REQUIRED: Task/subtask ID(s) to move
  "to": "125,126,127",                                      // REQUIRED: Destination ID(s)
  "file": "tasks/tasks.json"                               // Optional: Custom tasks file path
}
```

---

## 💡 **Usage Examples:**

### **Start a new project:**
```json
{
  "tool": "initialize_project_taskmaster-ai",
  "params": {
    "projectRoot": "C:\\Users\\<USER>\\my-project",
    "rules": ["cursor", "claude"]
  }
}
```

### **Parse a PRD and expand complex tasks:**
```json
{
  "tool": "parse_prd_taskmaster-ai",
  "params": {
    "projectRoot": "C:\\Users\\<USER>\\my-project",
    "numTasks": "20",
    "research": true
  }
}
```

### **Work on a specific task:**
```json
{
  "tool": "set_task_status_taskmaster-ai",
  "params": {
    "projectRoot": "C:\\Users\\<USER>\\my-project",
    "id": "5",
    "status": "in-progress"
  }
}
```

### **Expand a task with custom context:**
```json
{
  "tool": "expand_task_taskmaster-ai",
  "params": {
    "projectRoot": "C:\\Users\\<USER>\\my-project",
    "id": "10",
    "num": "6",
    "prompt": "Focus on error handling and security best practices",
    "research": true
  }
}
```

### **Research a topic and save to task:**
```json
{
  "tool": "research_taskmaster-ai",
  "params": {
    "projectRoot": "C:\\Users\\<USER>\\my-project",
    "query": "Best practices for Node.js authentication",
    "taskIds": "15,16",
    "detailLevel": "high",
    "saveTo": "15.3"
  }
}
```

### **Analyze project complexity:**
```json
{
  "tool": "analyze_project_complexity_taskmaster-ai",
  "params": {
    "projectRoot": "C:\\Users\\<USER>\\my-project",
    "threshold": 7,
    "research": true
  }
}
```

---

## 📝 **Important Notes:**

1. **projectRoot is ALWAYS required** - Must be absolute path to project directory
2. **Status values:** `pending`, `done`, `in-progress`, `review`, `deferred`, `cancelled`
3. **Priority values:** `high`, `medium`, `low`
4. **Subtask IDs:** Use format `"parentId.subtaskId"` (e.g., `"15.2"`)
5. **Multiple IDs:** Use comma-separated strings (e.g., `"107,108,109"`)
6. **Research flag:** Enables Perplexity AI for enhanced results
7. **All tools return structured JSON** with success/error information

**All tools return structured JSON responses with success/error information and relevant data!** 🎯
