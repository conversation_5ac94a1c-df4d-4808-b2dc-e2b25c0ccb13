# Task ID: 126
# Title: Implement Interactive Project Initialization Command
# Status: pending
# Dependencies: 107, 110
# Priority: medium
# Description: Create an interactive `init` command to set up a new project, including `tasks.json` metadata and `.env` file creation.
# Details:
Create an `init` command. Use a library like `inquirer` to prompt the user for Project Name and PRD Source. This information will be used to populate the `meta` block in `tasks.json`. The command should also copy `.env.example` to `.env` and instruct the user to fill it out.

# Test Strategy:
Run the `init` command. Respond to the interactive prompts. Verify that `tasks.json` is created with the correct metadata and that a `.env` file exists.
