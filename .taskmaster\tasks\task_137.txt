# Task ID: 137
# Title: Enhance API Test Command to Accept Key as Argument
# Status: pending
# Dependencies: 136, 131
# Priority: medium
# Description: Modify the `api:test` CLI command to optionally accept an API key directly as a command-line argument, overriding any environment variable.
# Details:
Update the `api:test <service>` command implemented in Task #136. Add an optional command-line argument, such as `--key <api_key>`. If this argument is provided, its value must be used for the API connectivity test. If the argument is omitted, the command should maintain its original behavior of reading the API key from the corresponding environment variable (e.g., `CLAUDE_API_KEY`). This allows for more flexible and explicit testing from the command line without needing to modify `.env` files.

# Test Strategy:
1. Set an invalid `CLAUDE_API_KEY` in the environment. Run `task-master api:test claude --key <VALID_KEY>` and verify a success message is printed. 2. Run `task-master api:test claude --key <INVALID_KEY>` and verify a clear authentication error is displayed, leveraging the error handling from Task #131. 3. Set a valid `CLAUDE_API_KEY` in the environment. Run `task-master api:test claude` without the `--key` argument and verify it succeeds by using the environment variable. 4. Remove the key from both the environment and the command line, and verify the command fails with a message indicating that no API key was found.
