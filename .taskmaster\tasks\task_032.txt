# Task ID: 32
# Title: Implement "learn" Command for Automatic Cursor Rule Generation
# Status: deferred
# Dependencies: None
# Priority: high
# Description: Create a new "learn" command that analyzes Cursor's chat history and code changes to automatically generate or update rule files in the .cursor/rules directory, following the cursor_rules.mdc template format. This command will help Cursor autonomously improve its ability to follow development standards by learning from successful implementations.
# Details:
Implement a new command in the task-master CLI that enables Cursor to learn from successful coding patterns and chat interactions:

Key Components:
1. Cursor Data Analysis
   - Access and parse Cursor's chat history from ~/Library/Application Support/Cursor/User/History
   - Extract relevant patterns, corrections, and successful implementations
   - Track file changes and their associated chat context

2. Rule Management
   - Use cursor_rules.mdc as the template for all rule file formatting
   - Manage rule files in .cursor/rules directory
   - Support both creation and updates of rule files
   - Categorize rules based on context (testing, components, API, etc.)

3. AI Integration
   - Utilize ai-services.js to interact with Claude
   - Provide comprehensive context including:
     * Relevant chat history showing the evolution of solutions
     * Code changes and their outcomes
     * Existing rules and template structure
   - Generate or update rules while maintaining template consistency

4. Implementation Requirements:
   - Automatic triggering after task completion (configurable)
   - Manual triggering via CLI command
   - Proper error handling for missing or corrupt files
   - Validation against cursor_rules.mdc template
   - Performance optimization for large histories
   - Clear logging and progress indication

5. Key Files:
   - commands/learn.js: Main command implementation
   - rules/cursor-rules-manager.js: Rule file management
   - utils/chat-history-analyzer.js: Cursor chat analysis
   - index.js: Command registration

6. Security Considerations:
   - Safe file system operations
   - Proper error handling for inaccessible files
   - Validation of generated rules
   - Backup of existing rules before updates

# Test Strategy:
1. Unit Tests:
   - Test each component in isolation:
     * Chat history extraction and analysis
     * Rule file management and validation
     * Pattern detection and categorization
     * Template validation logic
   - Mock file system operations and AI responses
   - Test error handling and edge cases

2. Integration Tests:
   - End-to-end command execution
   - File system interactions
   - AI service integration
   - Rule generation and updates
   - Template compliance validation

3. Manual Testing:
   - Test after completing actual development tasks
   - Verify rule quality and usefulness
   - Check template compliance
   - Validate performance with large histories
   - Test automatic and manual triggering

4. Validation Criteria:
   - Generated rules follow cursor_rules.mdc format
   - Rules capture meaningful patterns
   - Performance remains acceptable
   - Error handling works as expected
   - Generated rules improve Cursor's effectiveness

# Subtasks:
## 1. Create Initial File Structure [pending]
### Dependencies: None
### Description: Set up the basic file structure for the learn command implementation
### Details:
Create the following files with basic exports:
- commands/learn.js
- rules/cursor-rules-manager.js
- utils/chat-history-analyzer.js
- utils/cursor-path-helper.js

## 2. Implement Cursor Path Helper [pending]
### Dependencies: None
### Description: Create utility functions to handle Cursor's application data paths
### Details:
In utils/cursor-path-helper.js implement:
- getCursorAppDir(): Returns ~/Library/Application Support/Cursor
- getCursorHistoryDir(): Returns User/History path
- getCursorLogsDir(): Returns logs directory path
- validatePaths(): Ensures required directories exist

## 3. Create Chat History Analyzer Base [pending]
### Dependencies: None
### Description: Create the base structure for analyzing Cursor's chat history
### Details:
In utils/chat-history-analyzer.js create:
- ChatHistoryAnalyzer class
- readHistoryDir(): Lists all history directories
- readEntriesJson(): Parses entries.json files
- parseHistoryEntry(): Extracts relevant data from .js files

## 4. Implement Chat History Extraction [pending]
### Dependencies: None
### Description: Add core functionality to extract relevant chat history
### Details:
In ChatHistoryAnalyzer add:
- extractChatHistory(startTime): Gets history since task start
- parseFileChanges(): Extracts code changes
- parseAIInteractions(): Extracts AI responses
- filterRelevantHistory(): Removes irrelevant entries

## 5. Create CursorRulesManager Base [pending]
### Dependencies: None
### Description: Set up the base structure for managing Cursor rules
### Details:
In rules/cursor-rules-manager.js create:
- CursorRulesManager class
- readTemplate(): Reads cursor_rules.mdc
- listRuleFiles(): Lists all .mdc files
- readRuleFile(): Reads specific rule file

## 6. Implement Template Validation [pending]
### Dependencies: None
### Description: Add validation logic for rule files against cursor_rules.mdc
### Details:
In CursorRulesManager add:
- validateRuleFormat(): Checks against template
- parseTemplateStructure(): Extracts template sections
- validateAgainstTemplate(): Validates content structure
- getRequiredSections(): Lists mandatory sections

## 7. Add Rule Categorization Logic [pending]
### Dependencies: None
### Description: Implement logic to categorize changes into rule files
### Details:
In CursorRulesManager add:
- categorizeChanges(): Maps changes to rule files
- detectRuleCategories(): Identifies relevant categories
- getRuleFileForPattern(): Maps patterns to files
- createNewRuleFile(): Initializes new rule files

## 8. Implement Pattern Analysis [pending]
### Dependencies: None
### Description: Create functions to analyze implementation patterns
### Details:
In ChatHistoryAnalyzer add:
- extractPatterns(): Finds success patterns
- extractCorrections(): Finds error corrections
- findSuccessfulPaths(): Tracks successful implementations
- analyzeDecisions(): Extracts key decisions

## 9. Create AI Prompt Builder [pending]
### Dependencies: None
### Description: Implement prompt construction for Claude
### Details:
In learn.js create:
- buildRuleUpdatePrompt(): Builds Claude prompt
- formatHistoryContext(): Formats chat history
- formatRuleContext(): Formats current rules
- buildInstructions(): Creates specific instructions

## 10. Implement Learn Command Core [pending]
### Dependencies: None
### Description: Create the main learn command implementation
### Details:
In commands/learn.js implement:
- learnCommand(): Main command function
- processRuleUpdates(): Handles rule updates
- generateSummary(): Creates learning summary
- handleErrors(): Manages error cases

## 11. Add Auto-trigger Support [pending]
### Dependencies: None
### Description: Implement automatic learning after task completion
### Details:
Update task-manager.js:
- Add autoLearnConfig handling
- Modify completeTask() to trigger learning
- Add learning status tracking
- Implement learning queue

## 12. Implement CLI Integration [pending]
### Dependencies: None
### Description: Add the learn command to the CLI
### Details:
Update index.js to:
- Register learn command
- Add command options
- Handle manual triggers
- Process command flags

## 13. Add Progress Logging [pending]
### Dependencies: None
### Description: Implement detailed progress logging
### Details:
Create utils/learn-logger.js with:
- logLearningProgress(): Tracks overall progress
- logRuleUpdates(): Tracks rule changes
- logErrors(): Handles error logging
- createSummary(): Generates final report

## 14. Implement Error Recovery [pending]
### Dependencies: None
### Description: Add robust error handling throughout the system
### Details:
Create utils/error-handler.js with:
- handleFileErrors(): Manages file system errors
- handleParsingErrors(): Manages parsing failures
- handleAIErrors(): Manages Claude API errors
- implementRecoveryStrategies(): Adds recovery logic

## 15. Add Performance Optimization [pending]
### Dependencies: None
### Description: Optimize performance for large histories
### Details:
Add to utils/performance-optimizer.js:
- implementCaching(): Adds result caching
- optimizeFileReading(): Improves file reading
- addProgressiveLoading(): Implements lazy loading
- addMemoryManagement(): Manages memory usage

