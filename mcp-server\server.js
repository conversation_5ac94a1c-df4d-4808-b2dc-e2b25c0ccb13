#!/usr/bin/env node

import TaskMasterMCPServer from './src/index.js';
import dotenv from 'dotenv';
import logger from './src/logger.js';

// Parse command-line arguments manually
function parseArgs() {
	const args = process.argv.slice(2);
	const options = {};

	for (let i = 0; i < args.length; i++) {
		const arg = args[i];

		if (arg === '--help' || arg === '-h') {
			console.log(`
Usage: task-master-mcp-server [options]

Task Master MCP Server

Options:
  --google-api-key <key>        Google API key for Gemini models
  --anthropic-api-key <key>     Anthropic API key for Claude models
  --openai-api-key <key>        OpenAI API key
  --perplexity-api-key <key>    Perplexity API key
  --mistral-api-key <key>       Mistral API key
  --xai-api-key <key>           xAI API key
  --openrouter-api-key <key>    OpenRouter API key
  --azure-openai-api-key <key>  Azure OpenAI API key
  --ollama-api-key <key>        Ollama API key
  -h, --help                    display help for command
`);
			process.exit(0);
		}

		if (arg.startsWith('--') && i + 1 < args.length) {
			const key = arg.slice(2);
			const value = args[i + 1];
			options[key] = value;
			i++; // Skip the next argument as it's the value
		}
	}

	return options;
}

const options = parseArgs();

// Set API keys from command-line arguments into environment variables
// This allows the existing system to pick them up via process.env
if (options['google-api-key']) {
	process.env.GOOGLE_API_KEY = options['google-api-key'];
	logger.info('Google API key set from command-line argument');
}
if (options['anthropic-api-key']) {
	process.env.ANTHROPIC_API_KEY = options['anthropic-api-key'];
	logger.info('Anthropic API key set from command-line argument');
}
if (options['openai-api-key']) {
	process.env.OPENAI_API_KEY = options['openai-api-key'];
	logger.info('OpenAI API key set from command-line argument');
}
if (options['perplexity-api-key']) {
	process.env.PERPLEXITY_API_KEY = options['perplexity-api-key'];
	logger.info('Perplexity API key set from command-line argument');
}
if (options['mistral-api-key']) {
	process.env.MISTRAL_API_KEY = options['mistral-api-key'];
	logger.info('Mistral API key set from command-line argument');
}
if (options['xai-api-key']) {
	process.env.XAI_API_KEY = options['xai-api-key'];
	logger.info('xAI API key set from command-line argument');
}
if (options['openrouter-api-key']) {
	process.env.OPENROUTER_API_KEY = options['openrouter-api-key'];
	logger.info('OpenRouter API key set from command-line argument');
}
if (options['azure-openai-api-key']) {
	process.env.AZURE_OPENAI_API_KEY = options['azure-openai-api-key'];
	logger.info('Azure OpenAI API key set from command-line argument');
}
if (options['ollama-api-key']) {
	process.env.OLLAMA_API_KEY = options['ollama-api-key'];
	logger.info('Ollama API key set from command-line argument');
}

// Load environment variables from .env file (after setting CLI args)
// This allows .env to provide defaults for keys not specified via CLI
dotenv.config();

/**
 * Start the MCP server
 */
async function startServer() {
	const server = new TaskMasterMCPServer();

	// Handle graceful shutdown
	process.on('SIGINT', async () => {
		await server.stop();
		process.exit(0);
	});

	process.on('SIGTERM', async () => {
		await server.stop();
		process.exit(0);
	});

	try {
		await server.start();
		logger.info('MCP server started successfully');
	} catch (error) {
		logger.error(`Failed to start MCP server: ${error.message}`);
		process.exit(1);
	}
}

// Start the server
startServer();
