# Task ID: 113
# Title: Implement 'create' Command for New Tasks
# Status: pending
# Dependencies: 109, 110
# Priority: medium
# Description: Implement a CLI command to create a new task and add it to `tasks.json`.
# Details:
Add a `create` command that takes `--title` and `--description` as arguments. It should generate a new sequential ID, set default status to 'pending' and priority to 'medium', and append the new task to the `tasks` array in `tasks.json`.

# Test Strategy:
Run the `create` command with a title and description. Verify that a new task with the correct structure and the next available ID has been added to `tasks.json`.
