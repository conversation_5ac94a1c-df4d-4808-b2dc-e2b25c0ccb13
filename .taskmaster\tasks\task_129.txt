# Task ID: 129
# Title: Document Agent Workflow Guidelines
# Status: pending
# Dependencies: 127, 128
# Priority: low
# Description: Document the agent workflow, including task discovery, selection, implementation, and verification procedures.
# Details:
Expand upon `dev_workflow.mdc`. Document how an agent should use the CLI commands. Example sequence: `list --status=pending --priority=high --json` to find a task, then read `tasks/task_<id>.txt` for details, and finally `update --id=<id> --status=done` upon completion.

# Test Strategy:
Review the documentation from the perspective of an AI agent. Ensure the instructions are clear, unambiguous, and provide a complete workflow from start to finish.
