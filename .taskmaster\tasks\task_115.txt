# Task ID: 115
# Title: Implement Bi-directional Task File Synchronization
# Status: pending
# Dependencies: 114
# Priority: low
# Description: Implement bi-directional synchronization between `tasks.json` and the individual `.txt` task files.
# Details:
Create a `sync` command. If a `.txt` file is newer than `tasks.json`, parse the `.txt` file and update the corresponding task in `tasks.json`. If `tasks.json` is newer, regenerate the corresponding `.txt` file. This ensures both representations are kept in sync.

# Test Strategy:
Manually edit a `task_001.txt` file and run `sync`; verify `tasks.json` is updated. Then, use the `update` command to change task 1 and run `sync`; verify `task_001.txt` is updated.
