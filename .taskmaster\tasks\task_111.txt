# Task ID: 111
# Title: Implement 'list' Command for Tasks
# Status: pending
# Dependencies: 109, 110
# Priority: high
# Description: Implement a CLI command to list all tasks from `tasks.json` with colorized output for status and priority.
# Details:
Add a `list` command to `scripts/dev.js`. Use a library like `chalk` for colors. The command should read `tasks.json` using the file system utility and display tasks in a formatted table showing ID, Title, Status, Priority, and Dependencies.

# Test Strategy:
Create a mock `tasks.json` with several tasks. Run the `list` command and visually inspect the output for correctness and color-coding. Test with an empty tasks array.
