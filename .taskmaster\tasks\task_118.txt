# Task ID: 118
# Title: Implement PRD Parsing Command via Claude API
# Status: pending
# Dependencies: 109, 110, 117
# Priority: high
# Description: Create a CLI command that reads a PRD file, sends its content to the Claude API, and parses the response to create an initial `tasks.json` file.
# Details:
Create a `parse-prd` command that takes a file path as an argument. It will read the file content, wrap it in the 'PRD Parsing Prompt Structure' from the PRD, call the `claudeService`, and then parse the returned structured text or JSON to populate `tasks.json`.

# Test Strategy:
Use a sample PRD file. Run the command and mock the Claude API response. Verify that `tasks.json` is created and populated with tasks that correctly reflect the mock response.
