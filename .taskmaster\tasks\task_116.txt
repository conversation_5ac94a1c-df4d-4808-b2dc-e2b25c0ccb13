# Task ID: 116
# Title: Implement Configuration Management with .env
# Status: pending
# Dependencies: 107
# Priority: high
# Description: Set up configuration management using `.env` files to handle API keys and other settings.
# Details:
Install the `dotenv` package. Create a `src/config.ts` module that loads environment variables from `.env`. It should read `ANTHROPIC_API_KEY`, `PERPLEXITY_API_KEY`, `MODEL`, etc., and provide sensible defaults as specified in the PRD. Add `.env` to `.gitignore`.

# Test Strategy:
Create a `.env` file with test keys. Write unit tests for the config module to ensure it correctly loads variables and applies defaults when variables are not set.
