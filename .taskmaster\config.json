{"models": {"main": {"provider": "google", "modelId": "gemini-2.5-pro", "maxTokens": 1048000, "temperature": 0.2}, "research": {"provider": "google", "modelId": "gemini-2.5-pro", "maxTokens": 1048000, "temperature": 0.1}, "fallback": {"provider": "google", "modelId": "gemini-2.5-pro", "maxTokens": 1048000, "temperature": 0.2}}, "global": {"logLevel": "info", "debug": false, "defaultNumTasks": 10, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "responseLanguage": "English", "userId": "**********", "azureBaseURL": "https://your-endpoint.azure.com/", "defaultTag": "master"}, "claudeCode": {}}