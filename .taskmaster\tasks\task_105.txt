# Task ID: 105
# Title: Create Standalone Test Script for Gemini API Integration
# Status: pending
# Dependencies: 37
# Priority: medium
# Description: Create a dedicated, standalone test script to perform an end-to-end verification of the Gemini API integration. This script will confirm that the connection, authentication, and data exchange with the Gemini service are functioning correctly.
# Details:
1. Create a new test file, `test/verify_gemini.js`. This script should not be part of the automated test suite but a manual tool for verification.
2. The script must load the Gemini API key from the system's configuration, mirroring how the main application accesses it. It should include a clear error message and exit gracefully if the key is not found.
3. Instantiate the `GeminiService` class that was created as part of Task 37.
4. Formulate a simple, direct prompt, such as 'What is the capital of France?'.
5. Use the `GeminiService` instance to send this prompt to the Gemini API.
6. The script should print the raw response from the service to the console for inspection.
7. Implement basic validation logic to check if the response is a non-empty string and contains the expected answer (e.g., 'Paris').
8. Log clear status messages to the console, such as 'Attempting to connect to Gemini API...', 'Prompt sent successfully.', and 'Test PASSED' or 'Test FAILED' with the received response.

# Test Strategy:
1. Configure a valid Gemini API key in the local development environment.
2. Run the script from the command line: `node test/verify_gemini.js`.
3. Verify that the script outputs a successful connection message and a coherent response from the Gemini API that correctly answers the hardcoded question.
4. Temporarily modify the configuration to use an invalid API key.
5. Run the script again and verify that it fails gracefully with a clear error message about authentication failure, rather than crashing.
6. Remove the valid API key from the configuration entirely and run the script. Confirm it exits with a message indicating the key is missing.
