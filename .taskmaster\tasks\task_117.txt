# Task ID: 117
# Title: Develop Anthropic Claude API Integration Service
# Status: pending
# Dependencies: 116
# Priority: high
# Description: Create a dedicated service to interact with the Anthropic Claude API, handling authentication, requests, and responses.
# Details:
Install the `@anthropic-ai/sdk` package. Create a `src/claudeService.ts`. Implement a function `invokeClaude(prompt: string): Promise<string>` that takes a prompt, constructs the API request using the configured API key and model, sends it, and returns the parsed text response. Include error handling for API failures.

# Test Strategy:
Mock the Anthropic SDK. Write unit tests to verify that the service constructs the correct API request payload and handles both successful responses and API errors gracefully.

# Subtasks:
## 1. Set Up Project Environment and Secure Configuration for Claude API [pending]
### Dependencies: None
### Description: Prepare the project by installing the Anthropic SDK and establishing a secure method for managing the API key using environment variables.
### Details:
1. Install the `@anthropic-ai/sdk` package using your project's package manager (e.g., `npm install @anthropic-ai/sdk`).
2. Create a `.env` file in the project root to store the API key securely. Add `ANTHROPIC_API_KEY=your_api_key_here` to this file.
3. Ensure the `.env` file is listed in your `.gitignore` file to prevent committing secrets.
4. Create the service file at `src/claudeService.ts`.
5. Install and configure a library like `dotenv` to load environment variables into `process.env` at application startup.

## 2. Implement Claude Service Structure and Client Initialization [pending]
### Dependencies: None
### Description: Create the basic structure for the `claudeService.ts` module, including the initialization of the Anthropic SDK client using the securely loaded API key.
### Details:
In `src/claudeService.ts`, import the `Anthropic` class from `@anthropic-ai/sdk`. Define and export a singleton instance of the Anthropic client. This instance should be initialized by passing the API key from `process.env.ANTHROPIC_API_KEY`. Also, define the function signature for `invokeClaude(prompt: string): Promise<string>` but leave its implementation empty for now. This subtask focuses solely on setting up the authenticated client.

## 3. Implement Core API Request Logic (Happy Path) [pending]
### Dependencies: None
### Description: Implement the primary logic within the `invokeClaude` function to construct and send a request to the Anthropic API, assuming a successful response.
### Details:
Flesh out the `invokeClaude` function. Inside the function, use the initialized Anthropic client to call the `messages.create` method. Construct the request payload with the required parameters: `model` (e.g., 'claude-3-opus-20240229'), `max_tokens` (e.g., 1024), and `messages` (an array containing an object with `role: 'user'` and `content: prompt`). For now, the function can return the raw response object.

## 4. Implement Response Parsing Logic [pending]
### Dependencies: None
### Description: Enhance the `invokeClaude` function to parse the successful API response object and extract the primary text content, returning it as a string.
### Details:
Modify the `invokeClaude` function. After receiving the response from the `messages.create` call, access the response body to extract the generated text. The response content is typically in `response.content[0].text`. Add logic to handle cases where the content block might be empty or structured differently than expected. The function should now return the extracted text as a `string`, fulfilling its `Promise<string>` signature.

## 5. Implement Comprehensive API Error Handling [pending]
### Dependencies: None
### Description: Wrap the API call in robust error handling to gracefully manage potential failures from the Anthropic API, such as authentication errors, invalid requests, or server-side issues.
### Details:
Wrap the `client.messages.create` call within a `try...catch` block. In the `catch` block, inspect the error object. The `@anthropic-ai/sdk` throws specific error types like `AuthenticationError`, `PermissionDeniedError`, `NotFoundError`, etc. Log the specific error type and message for debugging purposes. Re-throw a custom application-level error (e.g., `ClaudeAPIError`) that wraps the original error, providing a consistent error interface to the rest of the application.

## 6. Implement Retry Logic for Rate Limiting and Finalize Service [pending]
### Dependencies: None
### Description: Add a simple retry mechanism with exponential backoff to handle rate limit errors. Finalize the service by adding JSDoc comments and ensuring it's ready for integration.
### Details:
Enhance the error handling logic. Specifically for `RateLimitError`, implement a retry mechanism. For example, attempt the call up to 3 times, waiting for an increasing amount of time between attempts (e.g., 1s, 2s, 4s). Use the error handling from the previous step for all other errors or after the final retry fails. Add comprehensive JSDoc comments to the `claudeService.ts` module and the `invokeClaude` function, explaining its purpose, parameters, return value, and potential errors. Ensure the module's exports are clean and ready for use.

