# Task ID: 123
# Title: Implement Research-Backed Subtask Generation
# Status: pending
# Dependencies: 120, 122
# Priority: medium
# Description: Enhance the task expansion feature to use the Perplexity API for generating more detailed, research-backed subtasks.
# Details:
Modify the `expand` command to include a `--research` flag. When this flag is present, the command should use the 'Research-Backed Expansion Prompt Structure' and call the `perplexityService`. The response should be parsed to create highly detailed subtasks with references.

# Test Strategy:
Run `expand --id=<id> --research`. Mock the Perplexity API response. Verify that the generated subtasks in `tasks.json` are more detailed and include references compared to the standard expansion.
