# Task ID: 121
# Title: Implement Implementation Drift Handling (Task Rewriting)
# Status: pending
# Dependencies: 112, 117
# Priority: low
# Description: Implement a mechanism to rewrite future, 'pending' tasks based on new context or completed work, preserving the integrity of the project plan.
# Details:
Create a `rewrite` command that takes `--id=<id>` and `--context="<text>"`. This command will fetch a pending task and its dependencies, prompt <PERSON> to update the task's details and test strategy based on the new context, and then update the task in `tasks.json`. It should not modify 'done' tasks.

# Test Strategy:
Mark some tasks as 'done'. Run `rewrite` on a 'pending' task with new context. Verify that only the pending task is updated and that completed tasks remain untouched.
