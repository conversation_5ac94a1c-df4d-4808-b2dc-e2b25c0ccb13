# Task ID: 125
# Title: Implement Task Filtering and Querying
# Status: pending
# Dependencies: 111
# Priority: low
# Description: Enhance the `list` command to support filtering tasks by status, priority, or other attributes.
# Details:
Add optional flags to the `list` command, such as `--status=<status>` and `--priority=<priority>`. The command logic will filter the tasks from `tasks.json` based on these criteria before displaying them.

# Test Strategy:
Create a diverse set of tasks in `tasks.json`. Run `list --status=pending --priority=high` and verify that only tasks matching both criteria are displayed in the output.
